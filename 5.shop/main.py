import pandas as pd
from sklearn.pipeline import Pipeline

df_XY = pd.read_csv('XY_shop.csv')
df_XY.info()
"""
 #   Column   Non-Null Count   Dtype  
---  ------   --------------   -----  
 0   Adm      500000 non-null  int64  
 1   AdmDur   500000 non-null  float64
 2   Inf      500000 non-null  int64  
 3   InfDur   500000 non-null  float64
 4   Prd      500000 non-null  int64  
 5   PrdDur   500000 non-null  float64
 6   BncRt    500000 non-null  float64
 7   ExtRt    500000 non-null  float64
 8   PgVal    500000 non-null  float64
 9   SpclDay  500000 non-null  float64
 10  Mo       500000 non-null  int64  
 11  OS       500000 non-null  int64  
 12  Bsr      500000 non-null  int64  
 13  Rgn      500000 non-null  int64  
 14  TfcTp    500000 non-null  int64  
 15  VstTp    500000 non-null  int64  
 16  Wkd      500000 non-null  int64  
 17  Rev      450000 non-null  float64
"""
numerical_features = ['Adm', 'AdmDur', 'Inf', 'InfDur', 'Prd', 'PrdDur', 'BncRt', 'ExtRt', 'PgVal', 'SpclDay', 'Mo', 'Wkd']
categorical_features = ['OS', 'Bsr', 'Rgn', 'TfcTp', 'VstTp']

Xy = df_XY[~df_XY.Rev.isna()]
X = Xy.drop('Rev', axis=1)
y = Xy['Rev']

X_valid = df_XY[df_XY.Rev.isna()].drop('Rev', axis=1)

Pipeline(

)

