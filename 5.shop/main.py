import pandas as pd
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

df_XY = pd.read_csv('XY_shop.csv')
df_XY.info()
"""
 #   Column   Non-Null Count   Dtype  
---  ------   --------------   -----  
 0   Adm      500000 non-null  int64  
 1   AdmDur   500000 non-null  float64
 2   Inf      500000 non-null  int64  
 3   InfDur   500000 non-null  float64
 4   Prd      500000 non-null  int64  
 5   PrdDur   500000 non-null  float64
 6   BncRt    500000 non-null  float64
 7   ExtRt    500000 non-null  float64
 8   PgVal    500000 non-null  float64
 9   SpclDay  500000 non-null  float64
 10  Mo       500000 non-null  int64  
 11  OS       500000 non-null  int64  
 12  Bsr      500000 non-null  int64  
 13  Rgn      500000 non-null  int64  
 14  TfcTp    500000 non-null  int64  
 15  VstTp    500000 non-null  int64  
 16  Wkd      500000 non-null  int64  
 17  Rev      450000 non-null  float64
"""
numerical_features = ['Adm', 'AdmDur', 'Inf', 'InfDur', 'Prd', 'PrdDur', 'BncRt', 'ExtRt', 'PgVal', 'SpclDay', 'Mo', 'Wkd']
categorical_features = ['OS', 'Bsr', 'Rgn', 'TfcTp', 'VstTp']

Xy = df_XY[~df_XY.Rev.isna()]
X = Xy.drop('Rev', axis=1)
y = Xy['Rev']

X_valid = df_XY[df_XY.Rev.isna()].drop('Rev', axis=1)

# Create preprocessing steps for different feature types
preprocessor = ColumnTransformer(
    transformers=[
        # Numerical features: apply standard scaling
        ('num', StandardScaler(), numerical_features),
        # Categorical features: apply one-hot encoding
        ('cat', OneHotEncoder(drop='first', sparse_output=False), categorical_features)
    ]
)

# Create the complete pipeline
pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('regressor', RandomForestRegressor(n_estimators=100, random_state=42))
])

# Split the data for training and validation
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Fit the pipeline
print("Training the pipeline...")
pipeline.fit(X_train, y_train)

# Make predictions
y_pred = pipeline.predict(X_test)

# Evaluate the model
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print(f"Mean Squared Error: {mse:.4f}")
print(f"R² Score: {r2:.4f}")

# Make predictions on validation set
print("Making predictions on validation set...")
y_valid_pred = pipeline.predict(X_valid)

# Create submission file (if needed)
submission = pd.DataFrame({
    'id': range(len(y_valid_pred)),
    'Rev': y_valid_pred
})
submission.to_csv('submission.csv', index=False)
print("Submission saved to submission.csv")

# Show feature names after preprocessing (optional)
print("\nFeature names after preprocessing:")
feature_names = (numerical_features +
                list(pipeline.named_steps['preprocessor']
                    .named_transformers_['cat']
                    .get_feature_names_out(categorical_features)))
print(f"Total features: {len(feature_names)}")
print("First 10 features:", feature_names[:10])

